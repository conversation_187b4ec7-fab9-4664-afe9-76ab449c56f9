@echo off
title Anagram Word Puzzle Game Launcher
color 0A

echo.
echo ========================================
echo    ANAGRAM WORD PUZZLE GAME LAUNCHER
echo ========================================
echo.
echo Starting the Anagram Word Puzzle Game...
echo.

REM Get the current directory
set "GAME_DIR=%~dp0"

REM Check if index.html exists
if not exist "%GAME_DIR%index.html" (
    echo ERROR: Game files not found!
    echo Please make sure all game files are in the same directory as this launcher.
    echo.
    pause
    exit /b 1
)

echo Game files found successfully!
echo.
echo Opening game in your default browser...
echo.

REM Try to open the game in the default browser
start "" "%GAME_DIR%index.html"

REM Wait a moment for the browser to start
timeout /t 2 /nobreak >nul

echo.
echo ========================================
echo    GAME LAUNCHED SUCCESSFULLY!
echo ========================================
echo.
echo The Anagram Word Puzzle game should now be
echo open in your default web browser.
echo.
echo GAME FEATURES:
echo - 50 English words across 10 levels
echo - Interactive learning with meanings and tips
echo - Sound effects and animations
echo - Progress tracking
echo - Mobile-friendly design
echo.
echo ADMIN PANEL:
echo To access the admin panel for managing
echo words and levels, open admin.html
echo.
echo CONTROLS:
echo - Click letters in correct order to spell words
echo - Use hints if you get stuck (costs points)
echo - Learn word meanings after each puzzle
echo.
echo Enjoy learning English vocabulary!
echo.
echo Press any key to close this launcher...
pause >nul
