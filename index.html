<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Anagram Word Puzzle - Learn English</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="game-container">
        <!-- Header -->
        <header class="game-header">
            <div class="header-content">
                <h1 class="game-title">
                    <i class="fas fa-puzzle-piece"></i>
                    Anagram Puzzle
                </h1>
                <div class="game-stats">
                    <div class="stat-item">
                        <i class="fas fa-star"></i>
                        <span id="score">0</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-layer-group"></i>
                        <span id="level">1</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-trophy"></i>
                        <span id="progress">1/5</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- Navigation -->
        <nav class="game-nav">
            <button class="nav-btn active" data-page="game">
                <i class="fas fa-gamepad"></i>
                <span>Game</span>
            </button>
            <button class="nav-btn" data-page="progress">
                <i class="fas fa-chart-line"></i>
                <span>Progress</span>
            </button>
            <button class="nav-btn" data-page="settings">
                <i class="fas fa-cog"></i>
                <span>Settings</span>
            </button>
        </nav>

        <!-- Game Page -->
        <main class="game-page active" id="game-page">
            <!-- Word Display Area -->
            <div class="word-display">
                <div class="word-hint">
                    <p id="word-meaning">Guess the word from the scrambled letters below!</p>
                </div>
                <div class="word-boxes" id="word-boxes">
                    <!-- Word boxes will be generated dynamically -->
                </div>
            </div>

            <!-- Scrambled Letters -->
            <div class="scrambled-section">
                <h3>Use these letters:</h3>
                <div class="scrambled-letters" id="scrambled-letters">
                    <!-- Scrambled letters will be generated dynamically -->
                </div>
            </div>

            <!-- Game Controls -->
            <div class="game-controls">
                <button class="control-btn hint-btn" id="hint-btn">
                    <i class="fas fa-lightbulb"></i>
                    Hint (-5 pts)
                </button>
                <button class="control-btn reveal-btn" id="reveal-btn">
                    <i class="fas fa-eye"></i>
                    Reveal (-10 pts)
                </button>
                <button class="control-btn reset-btn" id="reset-btn">
                    <i class="fas fa-redo"></i>
                    Reset
                </button>
            </div>

            <!-- Learning Section -->
            <div class="learning-section" id="learning-section">
                <div class="learning-header">
                    <h2 id="learned-word">WORD</h2>
                    <div class="word-pronunciation" id="word-pronunciation">/pronunciation/</div>
                </div>

                <div class="tips-section" id="tips-section">
                    <div class="section-header">
                        <div class="icon-container">
                            <i class="fas fa-lightbulb section-icon"></i>
                            <div class="sparkle-container"></div>
                        </div>
                        <h3>How to Use</h3>
                        <button class="close-section-btn" onclick="game.closeLearningSection('tips')">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <p id="word-tips">Usage tips go here...</p>
                </div>

                <div class="fun-fact-section" id="fun-fact-section">
                    <div class="section-header">
                        <div class="icon-container">
                            <i class="fas fa-star section-icon"></i>
                            <div class="sparkle-container"></div>
                        </div>
                        <h3>Did You Know?</h3>
                        <button class="close-section-btn" onclick="game.closeLearningSection('fun-fact')">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <p id="word-fun-fact">Fun fact goes here...</p>
                </div>

                <div class="learning-footer">
                    <button class="next-word-btn" id="next-word-btn">
                        <i class="fas fa-arrow-right"></i>
                        Next Word
                    </button>
                </div>
            </div>
        </main>

        <!-- Progress Page -->
        <div class="game-page" id="progress-page">
            <div class="progress-content">
                <h2><i class="fas fa-chart-line"></i> Your Progress</h2>
                <div class="level-progress" id="level-progress">
                    <!-- Progress will be generated dynamically -->
                </div>
                <div class="achievements">
                    <h3><i class="fas fa-trophy"></i> Achievements</h3>
                    <div class="achievement-grid" id="achievement-grid">
                        <!-- Achievements will be generated dynamically -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings Page -->
        <div class="game-page" id="settings-page">
            <div class="settings-content">
                <h2><i class="fas fa-cog"></i> Settings</h2>
                <div class="setting-group">
                    <label class="setting-label">
                        <i class="fas fa-volume-up"></i>
                        Sound Effects
                        <input type="checkbox" id="sound-toggle" checked>
                        <span class="toggle-slider"></span>
                    </label>
                </div>
                <div class="setting-group">
                    <label class="setting-label">
                        <i class="fas fa-palette"></i>
                        Theme
                        <select id="theme-select">
                            <option value="default">Default</option>
                            <option value="dark">Dark Mode</option>
                            <option value="colorful">Colorful</option>
                        </select>
                    </label>
                </div>
                <div class="setting-group">
                    <button class="reset-progress-btn" id="reset-progress-btn">
                        <i class="fas fa-trash"></i>
                        Reset All Progress
                    </button>
                </div>
            </div>
        </div>

        <!-- Confetti Container -->
        <div class="confetti-container" id="confetti-container"></div>

        <!-- Loading Screen -->
        <div class="loading-screen" id="loading-screen">
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <p>Loading your word puzzle...</p>
            </div>
        </div>
    </div>

    <!-- Audio Elements -->
    <audio id="correct-sound" preload="auto">
        <source src="audio/correct.mp3" type="audio/mpeg">
    </audio>
    <audio id="wrong-sound" preload="auto">
        <source src="audio/wrong.mp3" type="audio/mpeg">
    </audio>
    <audio id="victory-sound" preload="auto">
        <source src="audio/victory.mp3" type="audio/mpeg">
    </audio>

    <script src="script.js"></script>
</body>
</html>
