@echo off
title Anagram Word Puzzle - Admin Panel Launcher
color 0C

echo.
echo ========================================
echo    ANAGRAM PUZZLE - ADMIN PANEL
echo ========================================
echo.
echo Starting the Admin Panel...
echo.

REM Get the current directory
set "GAME_DIR=%~dp0"

REM Check if admin.html exists
if not exist "%GAME_DIR%admin.html" (
    echo ERROR: Admin panel files not found!
    echo Please make sure all files are in the same directory as this launcher.
    echo.
    pause
    exit /b 1
)

echo Admin panel files found successfully!
echo.
echo Opening admin panel in your default browser...
echo.

REM Try to open the admin panel in the default browser
start "" "%GAME_DIR%admin.html"

REM Wait a moment for the browser to start
timeout /t 2 /nobreak >nul

echo.
echo ========================================
echo    ADMIN PANEL LAUNCHED SUCCESSFULLY!
echo ========================================
echo.
echo The Admin Panel should now be open in your
echo default web browser.
echo.
echo ADMIN FEATURES:
echo - Add, edit, and delete levels
echo - Manage words and their properties
echo - Set word meanings, tips, and fun facts
echo - Import/Export game data
echo - Full CRUD operations
echo.
echo USAGE INSTRUCTIONS:
echo 1. Use "Manage Levels" to create/edit levels
echo 2. Use "Manage Words" to add/edit words
echo 3. Use "Import/Export" to backup your data
echo 4. Click "Save All Changes" to persist changes
echo.
echo WORD MANAGEMENT:
echo - Word: The target word to guess
echo - Scrambled: Letters available to player
echo - Hints: Pattern with some letters revealed
echo - Meaning: Definition of the word
echo - Tips: How to use the word
echo - Fun Fact: Interesting information
echo.
echo Remember to save your changes before closing!
echo.
echo Press any key to close this launcher...
pause >nul
