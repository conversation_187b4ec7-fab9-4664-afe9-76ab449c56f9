/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    overflow-x: hidden;
}

.game-container {
    max-width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    position: relative;
}

/* Header Styles */
.game-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 1rem;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

.game-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #667eea;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.game-stats {
    display: flex;
    gap: 1rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.3rem;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 0.5rem 0.8rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

/* Navigation Styles */
.game-nav {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    padding: 0.5rem;
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    box-shadow: 0 -2px 20px rgba(0, 0, 0, 0.1);
}

.nav-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.2rem;
    padding: 0.8rem 1.2rem;
    border: none;
    background: transparent;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.8rem;
    color: #666;
}

.nav-btn.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.nav-btn:hover:not(.active) {
    background: rgba(102, 126, 234, 0.1);
    transform: translateY(-1px);
}

/* Game Page Styles */
.game-page {
    flex: 1;
    padding: 2rem 1rem;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
    display: none;
}

.game-page.active {
    display: block;
}

/* Word Display */
.word-display {
    text-align: center;
    margin-bottom: 2rem;
}

.word-hint {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    padding: 1.5rem;
    border-radius: 20px;
    margin-bottom: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.word-hint p {
    font-size: 1.1rem;
    color: #555;
    line-height: 1.6;
}

.word-boxes {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.word-box {
    width: 50px;
    height: 50px;
    border: 3px solid #667eea;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: #667eea;
    background: white;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.word-box.filled {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.word-box.hint {
    background: linear-gradient(135deg, #ffeaa7, #fdcb6e);
    border-color: #fdcb6e;
    color: #d63031;
}

.word-box.correct-animation {
    animation: correctPulse 0.6s ease;
}

.word-box.wrong-animation {
    animation: wrongShake 0.6s ease;
    border-color: #d63031;
    background: rgba(214, 48, 49, 0.1);
}

/* Scrambled Letters */
.scrambled-section {
    text-align: center;
    margin-bottom: 2rem;
}

.scrambled-section h3 {
    color: white;
    margin-bottom: 1rem;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.scrambled-letters {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.letter-box {
    width: 50px;
    height: 50px;
    background: white;
    border: 3px solid #667eea;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: #667eea;
    cursor: pointer;
    transition: all 0.3s ease;
    user-select: none;
    position: relative;
    overflow: hidden;
}

.letter-box:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.letter-box.used {
    opacity: 0.3;
    transform: scale(0.8);
    cursor: not-allowed;
}

.letter-box.moving {
    z-index: 1000;
    pointer-events: none;
}

/* Game Controls */
.game-controls {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
    margin-bottom: 2rem;
}

.control-btn {
    padding: 0.8rem 1.5rem;
    border: none;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.hint-btn {
    background: linear-gradient(135deg, #ffeaa7, #fdcb6e);
    color: #d63031;
}

.reveal-btn {
    background: linear-gradient(135deg, #fd79a8, #e84393);
    color: white;
}

.reset-btn {
    background: linear-gradient(135deg, #74b9ff, #0984e3);
    color: white;
}

.control-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.control-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* Animations */
@keyframes correctPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); background: #00b894; }
    100% { transform: scale(1.05); }
}

@keyframes wrongShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

@keyframes letterMove {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .game-title {
        font-size: 1.2rem;
    }
    
    .game-stats {
        gap: 0.5rem;
    }
    
    .stat-item {
        padding: 0.4rem 0.6rem;
        font-size: 0.8rem;
    }
    
    .word-box, .letter-box {
        width: 45px;
        height: 45px;
        font-size: 1.3rem;
    }
    
    .game-controls {
        gap: 0.5rem;
    }
    
    .control-btn {
        padding: 0.6rem 1rem;
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .game-container {
        padding: 0;
    }
    
    .game-page {
        padding: 1rem 0.5rem;
    }
    
    .word-box, .letter-box {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }
    
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }
    
    .game-title {
        font-size: 1.1rem;
    }
}

/* Learning Modal Styles */
.learning-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 1rem;
}

.learning-modal.active {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 20px;
    max-width: 600px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease;
}

.modal-header {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 2rem;
    text-align: center;
    border-radius: 20px 20px 0 0;
}

.modal-header h2 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.word-pronunciation {
    font-style: italic;
    opacity: 0.9;
    font-size: 1.1rem;
}

.modal-body {
    padding: 2rem;
}

.meaning-section,
.tips-section,
.fun-fact-section,
.interactive-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    border-radius: 15px;
    border-left: 4px solid;
    opacity: 0;
    transform: translateY(30px);
    animation: sectionSlideIn 0.6s ease forwards;
}

.meaning-section {
    animation-delay: 0.2s;
}

.tips-section {
    animation-delay: 0.4s;
}

.fun-fact-section {
    animation-delay: 0.6s;
}

.interactive-section {
    animation-delay: 0.8s;
}

.meaning-section {
    background: rgba(102, 126, 234, 0.1);
    border-left-color: #667eea;
}

.tips-section {
    background: rgba(255, 234, 167, 0.3);
    border-left-color: #fdcb6e;
}

.fun-fact-section {
    background: rgba(253, 121, 168, 0.1);
    border-left-color: #fd79a8;
}

.interactive-section {
    background: rgba(116, 185, 255, 0.1);
    border-left-color: #74b9ff;
}

.modal-body h3 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    color: #333;
    font-weight: 600;
}

.modal-body p {
    line-height: 1.6;
    color: #555;
}

.practice-sentence {
    background: rgba(255, 255, 255, 0.8);
    padding: 1rem;
    border-radius: 10px;
    margin: 1rem 0;
    border: 2px dashed #74b9ff;
    min-height: 60px;
    font-weight: 500;
}

.practice-options {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-top: 1rem;
}

.practice-option {
    background: white;
    border: 2px solid #ddd;
    border-radius: 8px;
    padding: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: left;
    font-weight: 500;
}

.practice-option:hover {
    border-color: #74b9ff;
    background: rgba(116, 185, 255, 0.1);
    transform: translateX(5px);
}

.practice-option.selected {
    border-color: #74b9ff;
    background: rgba(116, 185, 255, 0.2);
}

.practice-option.correct {
    border-color: #00b894;
    background: rgba(0, 184, 148, 0.2);
    color: #00b894;
}

.practice-option.incorrect {
    border-color: #e17055;
    background: rgba(225, 112, 85, 0.2);
    color: #e17055;
}

.practice-btn {
    background: linear-gradient(135deg, #00b894, #00a085);
    color: white;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.practice-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 184, 148, 0.3);
}

.practice-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.modal-footer {
    padding: 1.5rem 2rem;
    text-align: center;
    border-top: 1px solid #eee;
}

.next-word-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 25px;
    font-weight: 600;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0 auto;
}

.next-word-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.next-word-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Progress Page Styles */
.progress-content {
    text-align: center;
}

.progress-content h2 {
    color: white;
    margin-bottom: 2rem;
    font-size: 2rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.level-progress {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.level-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.level-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.level-card.locked {
    opacity: 0.6;
    cursor: not-allowed;
}

.level-card.completed {
    border: 3px solid #00b894;
}

.level-title {
    font-size: 1.3rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 1rem;
}

.level-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    color: #666;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #eee;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 4px;
    transition: width 0.3s ease;
}

/* Confetti Animation */
.confetti-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 999;
}

.confetti {
    position: absolute;
    width: 10px;
    height: 10px;
    background: #667eea;
    animation: confettiFall 3s linear infinite;
    border-radius: 2px;
}

.confetti-circle {
    border-radius: 50%;
}

.confetti-square {
    border-radius: 2px;
}

.confetti-triangle {
    width: 0;
    height: 0;
    background: transparent;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-bottom: 10px solid #667eea;
}

@keyframes confettiFall {
    0% {
        transform: translateY(-100vh) rotate(0deg) scale(1);
        opacity: 1;
    }
    50% {
        opacity: 1;
        transform: translateY(50vh) rotate(360deg) scale(1.2);
    }
    100% {
        transform: translateY(100vh) rotate(720deg) scale(0.8);
        opacity: 0;
    }
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes sectionSlideIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease;
}

.loading-screen.hidden {
    opacity: 0;
    pointer-events: none;
}

.loading-content {
    text-align: center;
    color: white;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
