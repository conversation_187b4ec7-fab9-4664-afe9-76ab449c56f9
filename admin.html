<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel - Anagram Word Puzzle</title>
    <link rel="stylesheet" href="admin-styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-container">
        <!-- Header -->
        <header class="admin-header">
            <div class="header-content">
                <h1 class="admin-title">
                    <i class="fas fa-cog"></i>
                    Admin Panel - Anagram Puzzle
                </h1>
                <div class="header-actions">
                    <button class="btn btn-primary" id="save-all-btn">
                        <i class="fas fa-save"></i>
                        Save All Changes
                    </button>
                    <a href="index.html" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i>
                        Back to Game
                    </a>
                </div>
            </div>
        </header>

        <!-- Navigation Tabs -->
        <nav class="admin-nav">
            <button class="nav-tab active" data-tab="levels">
                <i class="fas fa-layer-group"></i>
                Manage Levels
            </button>
            <button class="nav-tab" data-tab="words">
                <i class="fas fa-spell-check"></i>
                Manage Words
            </button>
            <button class="nav-tab" data-tab="import-export">
                <i class="fas fa-exchange-alt"></i>
                Import/Export
            </button>
        </nav>

        <!-- Levels Management Tab -->
        <div class="tab-content active" id="levels-tab">
            <div class="section-header">
                <h2><i class="fas fa-layer-group"></i> Levels Management</h2>
                <button class="btn btn-success" id="add-level-btn">
                    <i class="fas fa-plus"></i>
                    Add New Level
                </button>
            </div>
            
            <div class="levels-grid" id="levels-grid">
                <!-- Levels will be populated dynamically -->
            </div>
        </div>

        <!-- Words Management Tab -->
        <div class="tab-content" id="words-tab">
            <div class="section-header">
                <h2><i class="fas fa-spell-check"></i> Words Management</h2>
                <div class="level-selector">
                    <label for="level-select">Select Level:</label>
                    <select id="level-select">
                        <!-- Options will be populated dynamically -->
                    </select>
                    <button class="btn btn-success" id="add-word-btn">
                        <i class="fas fa-plus"></i>
                        Add New Word
                    </button>
                </div>
            </div>
            
            <div class="words-grid" id="words-grid">
                <!-- Words will be populated dynamically -->
            </div>
        </div>

        <!-- Import/Export Tab -->
        <div class="tab-content" id="import-export-tab">
            <div class="section-header">
                <h2><i class="fas fa-exchange-alt"></i> Import/Export Data</h2>
            </div>
            
            <div class="import-export-content">
                <div class="export-section">
                    <h3><i class="fas fa-download"></i> Export Data</h3>
                    <p>Download the current game data as a JSON file for backup or sharing.</p>
                    <button class="btn btn-primary" id="export-btn">
                        <i class="fas fa-download"></i>
                        Export Game Data
                    </button>
                </div>
                
                <div class="import-section">
                    <h3><i class="fas fa-upload"></i> Import Data</h3>
                    <p>Upload a JSON file to replace the current game data. <strong>Warning:</strong> This will overwrite all existing data!</p>
                    <input type="file" id="import-file" accept=".json" style="display: none;">
                    <button class="btn btn-warning" id="import-btn">
                        <i class="fas fa-upload"></i>
                        Import Game Data
                    </button>
                </div>
            </div>
        </div>

        <!-- Level Modal -->
        <div class="modal" id="level-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="level-modal-title">Add New Level</h3>
                    <button class="close-btn" id="close-level-modal">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="level-form">
                        <div class="form-group">
                            <label for="level-number">Level Number:</label>
                            <input type="number" id="level-number" min="1" required>
                        </div>
                        <div class="form-group">
                            <label for="level-title">Level Title:</label>
                            <input type="text" id="level-title" placeholder="e.g., Beginner Words" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" id="cancel-level">Cancel</button>
                    <button class="btn btn-primary" id="save-level">Save Level</button>
                </div>
            </div>
        </div>

        <!-- Word Modal -->
        <div class="modal" id="word-modal">
            <div class="modal-content large">
                <div class="modal-header">
                    <h3 id="word-modal-title">Add New Word</h3>
                    <button class="close-btn" id="close-word-modal">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="word-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="word-text">Word:</label>
                                <input type="text" id="word-text" placeholder="EXAMPLE" required style="text-transform: uppercase;">
                            </div>
                            <div class="form-group">
                                <label for="word-points">Points:</label>
                                <input type="number" id="word-points" min="1" value="10" required>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="word-scrambled">Scrambled Letters (comma separated):</label>
                            <input type="text" id="word-scrambled" placeholder="E,X,A,M,P,L,E" required>
                            <small>Enter the letters that will be available to the player</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="word-hints">Hints Pattern:</label>
                            <input type="text" id="word-hints" placeholder="E,_,_,_,_,_,E" required>
                            <small>Use letters for hints and _ for blanks (comma separated)</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="word-meaning">Meaning:</label>
                            <textarea id="word-meaning" rows="3" placeholder="The definition of the word..." required></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="word-tips">Usage Tips:</label>
                            <textarea id="word-tips" rows="3" placeholder="How to use this word in sentences..." required></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="word-fun-fact">Did You Know? (Fun Fact):</label>
                            <textarea id="word-fun-fact" rows="3" placeholder="Interesting fact about this word..." required></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" id="cancel-word">Cancel</button>
                    <button class="btn btn-primary" id="save-word">Save Word</button>
                </div>
            </div>
        </div>

        <!-- Confirmation Modal -->
        <div class="modal" id="confirm-modal">
            <div class="modal-content small">
                <div class="modal-header">
                    <h3>Confirm Action</h3>
                </div>
                <div class="modal-body">
                    <p id="confirm-message">Are you sure you want to perform this action?</p>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" id="confirm-cancel">Cancel</button>
                    <button class="btn btn-danger" id="confirm-ok">Confirm</button>
                </div>
            </div>
        </div>

        <!-- Loading Overlay -->
        <div class="loading-overlay" id="loading-overlay">
            <div class="loading-spinner"></div>
            <p>Processing...</p>
        </div>

        <!-- Success/Error Messages -->
        <div class="message-container" id="message-container"></div>
    </div>

    <script src="admin-script.js"></script>
</body>
</html>
